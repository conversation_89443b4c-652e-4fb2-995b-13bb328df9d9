<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--区域数据-->
      <el-col :span="4" :xs="24">
        <div class="head-container">
          <el-input
            v-model="areaName"
            placeholder="输入关键字以查找"
            clearable
            size="small"
            prefix-icon="el-icon-search"
            style="margin-bottom: 20px"
          />
        </div>
        <div class="head-container">
          <el-tree
            :data="meterTreeOptions"
            :props="props"
            :expand-on-click-node="false"
            :filter-node-method="filterNode"
            ref="tree"
            node-key="id"
            show-checkbox
            default-expand-all
            highlight-current
          />
        </div>
      </el-col>
      <el-col :span="20" :xs="24">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px">
          <el-form-item label="点位类型" prop="pointType">
            <el-select
              v-model="queryParams.pointType"
              placeholder="请选择"
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="option in dict.type.point_type"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="时间范围">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期时间"
              end-placeholder="结束日期时间"
              value-format="yyyy-MM-dd HH:mm:ss"
              @change="handleDateRangeChange"
            ></el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            <el-button type="warning" icon="el-icon-setting" size="mini" @click="openTimeConfigDialog">峰平谷时段配置</el-button>
          </el-form-item>
        </el-form>

        <!-- 峰平谷图表区域 -->
        <!-- 峰时段图表 -->
        <el-row :gutter="20">
          <el-col :span="24">
            <div class="chart-container">
              <div class="chart-header">
                <span class="chart-title">峰时段</span>
                <div class="chart-tools">
                  <el-tooltip content="下载图表" placement="top">
                    <i class="el-icon-download" @click="saveAsImage('peakChart')"></i>
                  </el-tooltip>
                </div>
              </div>
              <div ref="peakChart" class="chart-content" v-loading="loading"></div>
            </div>
          </el-col>
        </el-row>

        <!-- 平时段图表 -->
        <el-row :gutter="20">
          <el-col :span="24">
            <div class="chart-container">
              <div class="chart-header">
                <span class="chart-title">平时段</span>
                <div class="chart-tools">
                  <el-tooltip content="下载图表" placement="top">
                    <i class="el-icon-download" @click="saveAsImage('flatChart')"></i>
                  </el-tooltip>
                </div>
              </div>
              <div ref="flatChart" class="chart-content" v-loading="loading"></div>
            </div>
          </el-col>
        </el-row>

        <!-- 谷时段图表 -->
        <el-row :gutter="20">
          <el-col :span="24">
            <div class="chart-container">
              <div class="chart-header">
                <span class="chart-title">谷时段</span>
                <div class="chart-tools">
                  <el-tooltip content="下载图表" placement="top">
                    <i class="el-icon-download" @click="saveAsImage('lowChart')"></i>
                  </el-tooltip>
                </div>
              </div>
              <div ref="lowChart" class="chart-content" v-loading="loading"></div>
            </div>
          </el-col>
        </el-row>

      </el-col>
    </el-row>

    <!-- 峰平谷时段配置组件 -->
    <SubTimeConfig
      v-model="timeConfigVisible"
      @save-success="handleTimeConfigSaveSuccess"
    />
  </div>
</template>

<script>

import * as echarts from 'echarts'
import {subAnalyse} from "@/api/biz/subAnalyse";
import TimeAnalysisSelector from "@/components/TimeAnalysisSelector/index.vue";
import {meterTree} from "@/api/biz/meter";
import SubTimeConfig from "./subTimeConfig/index.vue";

export default {
  components: {TimeAnalysisSelector, SubTimeConfig},
  dicts: ['point_type', 'energy_type'],
  data() {
    return {
      // 加载状态
      loading: false,

      // 时间范围选择器
      dateRange: [],

      meterTreeOptions: [],
      props: {
        multiple: true, emitPath: false,
        value: 'id', // 绑定的值字段名
        label: 'name', // 显示的文字字段名
        children: 'children' // 子选项字段名
      },
      // 区域名称
      areaName: undefined,

      // 查询参数
      queryParams: {
        meterIds: [],
        pointType: null,
        startTime: null,
        endTime: null
      },

      // 图表实例
      peakChart: null,
      flatChart: null,
      lowChart: null,

      // 图表数据
      chartData: {
        peak: { times: [], energy: [] },
        flat: { times: [], energy: [] },
        low: { times: [], energy: [] }
      },

      // 单位配置
      unit: 'kWh',

      // 峰平谷时段配置相关
      timeConfigVisible: false

    }
  },
  watch: {
    // 根据名称筛选部门树
    areaName(val) {
      this.$refs.tree.filter(val)
    }
  },
  created() {
    this.getMeterTree()
  },
  mounted() {
    this.initCharts()
    // 监听窗口大小变化，重新调整图表大小
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    // 销毁图表实例，释放内存
    if (this.peakChart) {
      this.peakChart.dispose()
    }
    if (this.flatChart) {
      this.flatChart.dispose()
    }
    if (this.lowChart) {
      this.lowChart.dispose()
    }
    // 移除窗口大小变化监听器
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    // 获取区域表具树结构
    getMeterTree() {
      this.typeLoading = true
      meterTree({
        energyType: this.energyType
      }).then(res => {
        this.meterTreeOptions = res.data
        this.typeLoading = false
      })
    },
    getSubAnalyseData() {
      this.loading = true
      subAnalyse(this.queryParams).then(response => {
        console.log(response)
        if (response.code === 200 && response.data) {
          // 更新图表数据
          this.chartData = {
            peak: response.data.peak || { times: [], energy: [] },
            flat: response.data.flat || { times: [], energy: [] },
            low: response.data.low || { times: [], energy: [] }
          }
          // 更新图表显示
          this.updateCharts()
        }
        this.loading = false
      }).catch(error => {
        console.error('获取数据失败:', error)
        this.loading = false
      })
    },
    /** 重置按钮操作 */
    resetQuery() {
      // 重置时间选择器
      this.$refs.timeAnalysisSelector.reset()
      // 重置其他参数
      this.queryParams.meterIds = []
      this.queryParams.pageNum = 1
      this.queryParams.pageSize = 10

      // 清除树选中状态 - 使用setCheckedKeys清空所有选中项
      this.$refs.tree.setCheckedKeys([])

      // 清空表格数据
      this.tableData = []
      this.total = 0
      this.dateColumns = {}
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
    /** 搜索按钮操作 */
    handleQuery() {
      if (!this.verify()) {
        return
      }
      this.queryParams.pageNum = 1
      this.getSubAnalyseData()
    },
    verify() {
      // 检查必要参数
      if (!this.queryParams.startTime || !this.queryParams.endTime || !this.queryParams.pointType) {
        this.$message.warning('请选择表具、点位、时间范围');
        return false;
      }

      // 获取选中的节点
      const checkedNodes = this.$refs.tree.getCheckedNodes();

      // 筛选出类型为 "meter" 的节点 ID
      this.queryParams.meterIds = checkedNodes
        .filter(node => node.type === 'meter')
        .map(node => node.id);

      // 检查是否选择了表具
      if (this.queryParams.meterIds.length === 0) {
        this.$message.warning('请至少选择一个表具');
        return false;
      }
      return true;
    },
    // 时间范围选择器变化
    handleDateRangeChange(val) {
      if (val) {
        this.queryParams.startTime = val[0]
        this.queryParams.endTime = val[1]
      } else {
        this.queryParams.startTime = undefined
        this.queryParams.endTime = undefined
      }
    },

    // 初始化图表
    initCharts() {
      this.$nextTick(() => {
        // 初始化峰时段图表
        this.peakChart = echarts.init(this.$refs.peakChart)
        // 初始化平时段图表
        this.flatChart = echarts.init(this.$refs.flatChart)
        // 初始化谷时段图表
        this.lowChart = echarts.init(this.$refs.lowChart)

        // 设置初始配置
        this.updateCharts()
      })
    },

    // 更新图表数据
    updateCharts() {
      if (!this.peakChart || !this.flatChart || !this.lowChart) return

      // 峰时段图表配置
      const peakOption = this.getChartOption('峰时段用电量', this.chartData.peak, '#ff6b6b')
      this.peakChart.setOption(peakOption)

      // 平时段图表配置
      const flatOption = this.getChartOption('平时段用电量', this.chartData.flat, '#4ecdc4')
      this.flatChart.setOption(flatOption)

      // 谷时段图表配置
      const lowOption = this.getChartOption('谷时段用电量', this.chartData.low, '#45b7d1')
      this.lowChart.setOption(lowOption)
    },

    // 获取图表配置
    getChartOption(title, data, color) {
      return {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#6a7985'
            }
          },
          formatter: (params) => {
            const param = params[0]
            return `<div style="font-weight:bold">${param.name}</div>
                   <div style="margin-top:5px">
                     <span style="display:inline-block;margin-right:5px;border-radius:50%;width:10px;height:10px;background-color:${param.color}"></span>
                     ${param.seriesName}: ${param.value.toFixed(2)} ${this.unit}
                   </div>`
          }
        },
        grid: {
          left: '3%',
          right: '3%',
          bottom: '3%',
          top: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: data.times,
          boundaryGap: false,
          axisLabel: {
            formatter: (value) => {
              // 日期格式化处理，与表具分析页面保持一致
              if (!value) return '';
              try {
                const date = new Date(value);
                if (isNaN(date.getTime())) {
                  return value;
                }
                if (value.includes('-') || value.includes('/')) {
                  if (value.includes(':') || value.includes(' ')) {
                    return `${date.getMonth() + 1}/${date.getDate()} ${date.getHours()}时`;
                  } else if (value.length <= 7) {
                    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
                  } else {
                    return `${date.getMonth() + 1}/${date.getDate()}`;
                  }
                } else if (value.length === 4) {
                  return value;
                } else {
                  return value;
                }
              } catch (e) {
                console.error('Date formatting error:', e);
                return value;
              }
            },
            interval: 'auto',
            rotate: 0
          }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            formatter: `{value} ${this.unit}`
          },
          splitLine: {
            lineStyle: {
              type: 'dashed'
            }
          }
        },
        series: [{
          name: '数值',
          type: 'line',
          data: data.energy,
          smooth: true,
          symbol: 'emptyCircle',
          symbolSize: 6,
          lineStyle: {
            width: 3,
            color: color
          },
          itemStyle: {
            color: color,
            borderWidth: 2
          },
          areaStyle: {
            opacity: 0.1,
            color: color
          }
        }]
      }
    },

    // 处理窗口大小变化
    handleResize() {
      if (this.peakChart) {
        this.peakChart.resize()
      }
      if (this.flatChart) {
        this.flatChart.resize()
      }
      if (this.lowChart) {
        this.lowChart.resize()
      }
    },

    // 保存图表为图片
    saveAsImage(chartRef) {
      let chart = null
      let chartName = ''

      switch(chartRef) {
        case 'peakChart':
          chart = this.peakChart
          chartName = '峰时段'
          break
        case 'flatChart':
          chart = this.flatChart
          chartName = '平时段'
          break
        case 'lowChart':
          chart = this.lowChart
          chartName = '谷时段'
          break
      }

      if (chart) {
        const url = chart.getDataURL({
          pixelRatio: 2,
          backgroundColor: '#fff'
        })

        const link = document.createElement('a')
        link.download = `${chartName}_${new Date().getTime()}.png`
        link.href = url
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
      }
    },

    // 峰平谷时段配置相关方法
    // 打开时段配置对话框
    openTimeConfigDialog() {
      this.timeConfigVisible = true
    },

    // 时段配置保存成功回调
    handleTimeConfigSaveSuccess() {
      // 可以在这里添加保存成功后的处理逻辑
      // 比如重新加载图表数据等
      console.log('时段配置保存成功')
    },
  }
}

</script>


<style scoped>
.chart-container {
  background: #fff;
  padding: 20px;
  margin-top: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: visible;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.chart-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.chart-tools {
  display: flex;
  gap: 15px;
}

.chart-tools i {
  font-size: 16px;
  cursor: pointer;
  color: #606266;
}

.chart-tools i:hover {
  color: #409EFF;
}

.chart-content {
  width: 100%;
  height: 180px;
  position: relative;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chart-content {
    height: 160px;
  }
}


</style>
